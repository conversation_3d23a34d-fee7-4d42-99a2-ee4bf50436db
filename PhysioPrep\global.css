@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  --primary-100: #FF6B6B;
  --primary-200: #dd4d51;
  --primary-300: #8f001a;
  --accent-100: #00FFFF;
  --accent-200: #00999b;
  --text-100: #FFFFFF;
  --text-200: #e0e0e0;
  --bg-100: #0F0F0F;
  --bg-200: #1f1f1f;
  --bg-300: #353535;
  
  /* Additional semantic colors */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;
  
  /* Border and shadow colors */
  --border-light: rgba(255, 255, 255, 0.1);
  --border-medium: rgba(255, 255, 255, 0.2);
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.25);
  --shadow-strong: rgba(0, 0, 0, 0.5);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Font sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
}

.dark:root {
  --primary-100: #FF6B6B;
  --primary-200: #dd4d51;
  --primary-300: #8f001a;
  --accent-100: #00FFFF;
  --accent-200: #00999b;
  --text-100: #FFFFFF;
  --text-200: #e0e0e0;
  --bg-100: #0F0F0F;
  --bg-200: #1f1f1f;
  --bg-300: #353535;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-100);
  color: var(--text-100);
  font-size: var(--text-base);
  overflow-x: hidden;
}

/* Custom utility classes */
.text-primary {
  color: var(--primary-100);
}

.text-primary-dark {
  color: var(--primary-200);
}

.text-accent {
  color: var(--accent-100);
}

.bg-primary {
  background-color: var(--primary-100);
}

.bg-primary-dark {
  background-color: var(--primary-200);
}

.bg-accent {
  background-color: var(--accent-100);
}

.border-primary {
  border-color: var(--primary-100);
}

.border-accent {
  border-color: var(--accent-100);
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%);
}

.bg-gradient-accent {
  background: linear-gradient(135deg, var(--accent-100) 0%, var(--accent-200) 100%);
}

.bg-gradient-dark {
  background: linear-gradient(135deg, var(--bg-200) 0%, var(--bg-300) 100%);
}

/* Card styles */
.card {
  background-color: var(--bg-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-light);
  box-shadow: 0 4px 6px var(--shadow-light);
}

.card-elevated {
  background-color: var(--bg-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-medium);
  box-shadow: 0 10px 25px var(--shadow-medium);
}

/* Button styles */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.btn-primary {
  background-color: var(--primary-100);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-200);
  transform: translateY(-1px);
}

.btn-accent {
  background-color: var(--accent-100);
  color: var(--bg-100);
}

.btn-accent:hover {
  background-color: var(--accent-200);
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-100);
  border: 2px solid var(--border-medium);
}

.btn-outline:hover {
  background-color: var(--bg-300);
  border-color: var(--primary-100);
}

/* Input styles */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-300);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-100);
  font-size: var(--text-base);
  transition: border-color 0.2s ease-in-out;
}

.input:focus {
  outline: none;
  border-color: var(--primary-100);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.input::placeholder {
  color: var(--text-200);
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Loading spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-100);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .card {
    padding: var(--spacing-md);
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--text-sm);
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
  outline: 2px solid var(--primary-100);
  outline-offset: 2px;
}
