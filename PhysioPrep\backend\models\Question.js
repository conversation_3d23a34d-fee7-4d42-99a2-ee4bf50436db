const mongoose = require('mongoose');

const questionSchema = new mongoose.Schema({
  question: {
    type: String,
    required: [true, 'Question text is required'],
    trim: true,
    maxlength: [1000, 'Question cannot exceed 1000 characters']
  },
  options: [{
    text: {
      type: String,
      required: [true, 'Option text is required'],
      trim: true,
      maxlength: [500, 'Option text cannot exceed 500 characters']
    },
    isCorrect: {
      type: Boolean,
      default: false
    }
  }],
  explanation: {
    type: String,
    trim: true,
    maxlength: [2000, 'Explanation cannot exceed 2000 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  tier: {
    type: String,
    enum: ['free', 'premium'],
    required: [true, 'Question tier is required'],
    default: 'free'
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    required: [true, 'Difficulty level is required'],
    default: 'medium'
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  image: {
    url: {
      type: String,
      default: null
    },
    alt: {
      type: String,
      default: null
    }
  },
  stats: {
    totalAttempts: {
      type: Number,
      default: 0
    },
    correctAttempts: {
      type: Number,
      default: 0
    },
    averageTime: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for success rate
questionSchema.virtual('successRate').get(function() {
  if (this.stats.totalAttempts === 0) return 0;
  return Math.round((this.stats.correctAttempts / this.stats.totalAttempts) * 100);
});

// Virtual for correct answer
questionSchema.virtual('correctAnswer').get(function() {
  const correctOption = this.options.find(option => option.isCorrect);
  return correctOption ? correctOption.text : null;
});

// Virtual for correct answer index
questionSchema.virtual('correctAnswerIndex').get(function() {
  return this.options.findIndex(option => option.isCorrect);
});

// Indexes
questionSchema.index({ category: 1, tier: 1, isActive: 1 });
questionSchema.index({ difficulty: 1, isActive: 1 });
questionSchema.index({ tags: 1 });
questionSchema.index({ createdAt: -1 });

// Validation: Ensure exactly one correct answer
questionSchema.pre('save', function(next) {
  const correctAnswers = this.options.filter(option => option.isCorrect);
  
  if (correctAnswers.length !== 1) {
    return next(new Error('Question must have exactly one correct answer'));
  }
  
  if (this.options.length < 2 || this.options.length > 6) {
    return next(new Error('Question must have between 2 and 6 options'));
  }
  
  next();
});

// Method to update stats
questionSchema.methods.updateStats = function(isCorrect, timeSpent = null) {
  this.stats.totalAttempts += 1;
  
  if (isCorrect) {
    this.stats.correctAttempts += 1;
  }
  
  // Update average time if provided
  if (timeSpent !== null) {
    const totalTime = this.stats.averageTime * (this.stats.totalAttempts - 1);
    this.stats.averageTime = Math.round((totalTime + timeSpent) / this.stats.totalAttempts);
  }
};

// Method to get question for user (hide correct answer)
questionSchema.methods.getQuestionForUser = function() {
  const questionObj = this.toObject();
  
  // Remove isCorrect from options
  questionObj.options = questionObj.options.map(option => ({
    _id: option._id,
    text: option.text
  }));
  
  // Remove explanation and stats for security
  delete questionObj.explanation;
  delete questionObj.stats;
  delete questionObj.correctAnswer;
  delete questionObj.correctAnswerIndex;
  
  return questionObj;
};

// Static method to get random questions
questionSchema.statics.getRandomQuestions = function(filters = {}, limit = 10) {
  const pipeline = [
    { $match: { isActive: true, ...filters } },
    { $sample: { size: limit } },
    {
      $lookup: {
        from: 'categories',
        localField: 'category',
        foreignField: '_id',
        as: 'categoryInfo'
      }
    },
    {
      $project: {
        question: 1,
        options: {
          $map: {
            input: '$options',
            as: 'option',
            in: {
              _id: '$$option._id',
              text: '$$option.text'
            }
          }
        },
        category: 1,
        tier: 1,
        difficulty: 1,
        tags: 1,
        image: 1,
        categoryInfo: { $arrayElemAt: ['$categoryInfo', 0] }
      }
    }
  ];
  
  return this.aggregate(pipeline);
};

// Static method to get questions by category
questionSchema.statics.getQuestionsByCategory = function(categoryId, tier = null, limit = null) {
  const filters = { category: categoryId, isActive: true };
  if (tier) filters.tier = tier;
  
  let query = this.find(filters)
    .populate('category', 'name slug color')
    .sort({ createdAt: -1 });
  
  if (limit) query = query.limit(limit);
  
  return query;
};

// Static method for admin to get questions with full details
questionSchema.statics.getQuestionsForAdmin = function(filters = {}, page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  
  return this.find(filters)
    .populate('category', 'name slug')
    .populate('createdBy', 'name email')
    .populate('lastModifiedBy', 'name email')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

module.exports = mongoose.model('Question', questionSchema);
