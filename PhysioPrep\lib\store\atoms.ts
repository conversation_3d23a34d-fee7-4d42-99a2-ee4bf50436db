import { atom } from 'recoil';

// User and Authentication State
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin';
  subscription: {
    type: 'free' | 'premium';
    isActive: boolean;
    startDate?: string;
    endDate?: string;
  };
  profile: {
    avatar?: string;
    phone?: string;
    dateOfBirth?: string;
    institution?: string;
  };
  stats: {
    totalQuizzesTaken: number;
    totalTestsTaken: number;
    totalQuestionsAnswered: number;
    correctAnswers: number;
    averageScore: number;
    streak: {
      current: number;
      longest: number;
      lastActivity?: string;
    };
  };
  preferences: {
    notifications: {
      email: boolean;
      push: boolean;
    };
    theme: 'light' | 'dark' | 'system';
  };
  isPremium: boolean;
  accuracyPercentage: number;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
}

export const authState = atom<AuthState>({
  key: 'authState',
  default: {
    isAuthenticated: false,
    user: null,
    token: null,
    refreshToken: null,
    isLoading: false,
  },
});

// Categories State
export interface Category {
  id: string;
  name: string;
  description: string;
  slug: string;
  icon?: string;
  color: string;
  isActive: boolean;
  order: number;
  stats: {
    totalQuestions: number;
    freeQuestions: number;
    premiumQuestions: number;
    totalQuizzes: number;
    totalTests: number;
  };
  questionsByTier: {
    free: number;
    premium: number;
    total: number;
  };
}

export const categoriesState = atom<Category[]>({
  key: 'categoriesState',
  default: [],
});

export const selectedCategoryState = atom<Category | null>({
  key: 'selectedCategoryState',
  default: null,
});

// Questions State
export interface Question {
  id: string;
  question: string;
  options: Array<{
    id: string;
    text: string;
    isCorrect?: boolean;
  }>;
  explanation?: string;
  category: string;
  tier: 'free' | 'premium';
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
  image?: {
    url: string;
    alt: string;
  };
  stats?: {
    totalAttempts: number;
    correctAttempts: number;
    averageTime: number;
  };
  successRate?: number;
}

export const questionsState = atom<Question[]>({
  key: 'questionsState',
  default: [],
});

export const currentQuestionState = atom<Question | null>({
  key: 'currentQuestionState',
  default: null,
});

// Quiz State
export interface Quiz {
  id: string;
  title: string;
  description?: string;
  category: string;
  questions: string[];
  tier: 'free' | 'premium';
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  settings: {
    timeLimit?: number;
    showCorrectAnswers: boolean;
    showExplanations: boolean;
    randomizeQuestions: boolean;
    randomizeOptions: boolean;
    allowReview: boolean;
    passingScore: number;
  };
  tags: string[];
  image?: {
    url: string;
    alt: string;
  };
  stats: {
    totalAttempts: number;
    averageScore: number;
    averageTime: number;
    passRate: number;
  };
  questionCount: number;
  estimatedDuration: number;
}

export const quizzesState = atom<Quiz[]>({
  key: 'quizzesState',
  default: [],
});

export const currentQuizState = atom<Quiz | null>({
  key: 'currentQuizState',
  default: null,
});

// Test State
export interface Test {
  id: string;
  title: string;
  description?: string;
  category: string;
  questions: string[];
  tier: 'free' | 'premium';
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  settings: {
    timeLimit: number;
    questionCount: number;
    passingScore: number;
    showCorrectAnswers: boolean;
    showExplanations: boolean;
    allowReview: boolean;
    randomizeQuestions: boolean;
    randomizeOptions: boolean;
    negativeMarking: {
      enabled: boolean;
      penalty: number;
    };
  };
  tags: string[];
  image?: {
    url: string;
    alt: string;
  };
  stats: {
    totalAttempts: number;
    averageScore: number;
    averageTime: number;
    passRate: number;
    highestScore: number;
    lowestScore: number;
  };
  actualQuestionCount: number;
  estimatedDuration: number;
}

export const testsState = atom<Test[]>({
  key: 'testsState',
  default: [],
});

export const currentTestState = atom<Test | null>({
  key: 'currentTestState',
  default: null,
});

// Assessment Session State
export interface AssessmentSession {
  id: string;
  type: 'quiz' | 'test' | 'daily_question';
  assessmentId: string;
  questions: Question[];
  currentQuestionIndex: number;
  answers: Record<string, number>; // questionId -> selectedOptionIndex
  startTime: string;
  timeLimit?: number;
  timeRemaining?: number;
  isCompleted: boolean;
  isPaused: boolean;
  settings: {
    showCorrectAnswers: boolean;
    showExplanations: boolean;
    allowReview: boolean;
    randomizeQuestions: boolean;
    randomizeOptions: boolean;
  };
}

export const assessmentSessionState = atom<AssessmentSession | null>({
  key: 'assessmentSessionState',
  default: null,
});

// Daily Question State
export interface DailyQuestion {
  id: string;
  date: string;
  formattedDate: string;
  category: Category;
  tier: 'free' | 'premium';
  question: Question;
  stats: {
    totalAttempts: number;
    successRate: number;
  };
  hasAttempted: boolean;
  userAttempt?: {
    selectedAnswer: number;
    isCorrect: boolean;
    timeSpent: number;
    attemptedAt: string;
  };
}

export const dailyQuestionState = atom<DailyQuestion | null>({
  key: 'dailyQuestionState',
  default: null,
});

// Results State
export interface Result {
  id: string;
  type: 'quiz' | 'test' | 'daily_question';
  quiz?: Quiz;
  test?: Test;
  category: Category;
  score: {
    total: number;
    percentage: number;
    correct: number;
    incorrect: number;
    unanswered: number;
  };
  timing: {
    startTime: string;
    endTime: string;
    totalTime: number;
    timeLimit?: number;
  };
  status: 'completed' | 'abandoned' | 'timeout';
  passed: boolean;
  passingScore: number;
  grade: string;
  performanceLevel: string;
  createdAt: string;
}

export const resultsState = atom<Result[]>({
  key: 'resultsState',
  default: [],
});

export const currentResultState = atom<Result | null>({
  key: 'currentResultState',
  default: null,
});

// UI State
export interface UIState {
  isLoading: boolean;
  error: string | null;
  theme: 'light' | 'dark' | 'system';
  activeTab: string;
  showOnboarding: boolean;
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    duration?: number;
  }>;
}

export const uiState = atom<UIState>({
  key: 'uiState',
  default: {
    isLoading: false,
    error: null,
    theme: 'dark',
    activeTab: 'home',
    showOnboarding: true,
    notifications: [],
  },
});

// Network State
export interface NetworkState {
  isConnected: boolean;
  isOnline: boolean;
  connectionType: string | null;
}

export const networkState = atom<NetworkState>({
  key: 'networkState',
  default: {
    isConnected: true,
    isOnline: true,
    connectionType: null,
  },
});
