const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please enter a valid email'
    ]
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  subscription: {
    type: {
      type: String,
      enum: ['free', 'premium'],
      default: 'free'
    },
    startDate: {
      type: Date,
      default: null
    },
    endDate: {
      type: Date,
      default: null
    },
    isActive: {
      type: <PERSON>olean,
      default: false
    }
  },
  profile: {
    avatar: {
      type: String,
      default: null
    },
    phone: {
      type: String,
      default: null
    },
    dateOfBirth: {
      type: Date,
      default: null
    },
    institution: {
      type: String,
      default: null
    }
  },
  preferences: {
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      }
    },
    theme: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'system'
    }
  },
  stats: {
    totalQuizzesTaken: {
      type: Number,
      default: 0
    },
    totalTestsTaken: {
      type: Number,
      default: 0
    },
    totalQuestionsAnswered: {
      type: Number,
      default: 0
    },
    correctAnswers: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },
    streak: {
      current: {
        type: Number,
        default: 0
      },
      longest: {
        type: Number,
        default: 0
      },
      lastActivity: {
        type: Date,
        default: null
      }
    }
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String,
    default: null
  },
  passwordResetToken: {
    type: String,
    default: null
  },
  passwordResetExpires: {
    type: Date,
    default: null
  },
  lastLogin: {
    type: Date,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for subscription status
userSchema.virtual('isPremium').get(function() {
  return this.subscription.type === 'premium' && 
         this.subscription.isActive && 
         this.subscription.endDate > new Date();
});

// Virtual for accuracy percentage
userSchema.virtual('accuracyPercentage').get(function() {
  if (this.stats.totalQuestionsAnswered === 0) return 0;
  return Math.round((this.stats.correctAnswers / this.stats.totalQuestionsAnswered) * 100);
});

// Index for email lookup
userSchema.index({ email: 1 });

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to update stats
userSchema.methods.updateStats = function(isCorrect, score = null) {
  this.stats.totalQuestionsAnswered += 1;
  if (isCorrect) {
    this.stats.correctAnswers += 1;
  }
  
  // Update average score if provided
  if (score !== null) {
    const totalScores = this.stats.averageScore * (this.stats.totalQuizzesTaken + this.stats.totalTestsTaken);
    const newTotal = this.stats.totalQuizzesTaken + this.stats.totalTestsTaken + 1;
    this.stats.averageScore = Math.round((totalScores + score) / newTotal);
  }
  
  // Update streak
  const today = new Date();
  const lastActivity = this.stats.streak.lastActivity;
  
  if (!lastActivity || this.isNewDay(lastActivity, today)) {
    if (isCorrect) {
      this.stats.streak.current += 1;
      if (this.stats.streak.current > this.stats.streak.longest) {
        this.stats.streak.longest = this.stats.streak.current;
      }
    } else {
      this.stats.streak.current = 0;
    }
    this.stats.streak.lastActivity = today;
  }
};

// Helper method to check if it's a new day
userSchema.methods.isNewDay = function(lastDate, currentDate) {
  const last = new Date(lastDate);
  const current = new Date(currentDate);
  return last.toDateString() !== current.toDateString();
};

module.exports = mongoose.model('User', userSchema);
