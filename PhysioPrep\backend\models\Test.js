const mongoose = require('mongoose');

const testSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Test title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  questions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Question'
  }],
  tier: {
    type: String,
    enum: ['free', 'premium'],
    required: [true, 'Test tier is required'],
    default: 'free'
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard', 'mixed'],
    default: 'mixed'
  },
  settings: {
    timeLimit: {
      type: Number,
      required: [true, 'Time limit is required for tests'],
      min: [5, 'Time limit must be at least 5 minutes'],
      max: [300, 'Time limit cannot exceed 300 minutes']
    },
    questionCount: {
      type: Number,
      required: [true, 'Question count is required'],
      min: [5, 'Test must have at least 5 questions'],
      max: [200, 'Test cannot have more than 200 questions']
    },
    passingScore: {
      type: Number,
      default: 70,
      min: 0,
      max: 100
    },
    showCorrectAnswers: {
      type: Boolean,
      default: true
    },
    showExplanations: {
      type: Boolean,
      default: true
    },
    allowReview: {
      type: Boolean,
      default: true
    },
    randomizeQuestions: {
      type: Boolean,
      default: true
    },
    randomizeOptions: {
      type: Boolean,
      default: false
    },
    negativeMarking: {
      enabled: {
        type: Boolean,
        default: false
      },
      penalty: {
        type: Number,
        default: 0.25,
        min: 0,
        max: 1
      }
    }
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  image: {
    url: {
      type: String,
      default: null
    },
    alt: {
      type: String,
      default: null
    }
  },
  stats: {
    totalAttempts: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },
    averageTime: {
      type: Number,
      default: 0
    },
    passRate: {
      type: Number,
      default: 0
    },
    highestScore: {
      type: Number,
      default: 0
    },
    lowestScore: {
      type: Number,
      default: 100
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for actual question count
testSchema.virtual('actualQuestionCount').get(function() {
  return this.questions.length;
});

// Virtual for estimated duration
testSchema.virtual('estimatedDuration').get(function() {
  return this.settings.timeLimit;
});

// Indexes
testSchema.index({ category: 1, tier: 1, isActive: 1, isPublished: 1 });
testSchema.index({ difficulty: 1, isActive: 1 });
testSchema.index({ tags: 1 });
testSchema.index({ createdAt: -1 });

// Validation: Ensure test has enough questions
testSchema.pre('save', function(next) {
  if (this.isPublished) {
    if (this.questions.length < this.settings.questionCount) {
      return next(new Error(`Test must have at least ${this.settings.questionCount} questions`));
    }
  }
  next();
});

// Method to update stats
testSchema.methods.updateStats = function(score, timeSpent, passed) {
  this.stats.totalAttempts += 1;
  
  // Update average score
  const totalScore = this.stats.averageScore * (this.stats.totalAttempts - 1);
  this.stats.averageScore = Math.round((totalScore + score) / this.stats.totalAttempts);
  
  // Update average time
  const totalTime = this.stats.averageTime * (this.stats.totalAttempts - 1);
  this.stats.averageTime = Math.round((totalTime + timeSpent) / this.stats.totalAttempts);
  
  // Update pass rate
  if (passed) {
    const totalPassed = Math.round((this.stats.passRate / 100) * (this.stats.totalAttempts - 1)) + 1;
    this.stats.passRate = Math.round((totalPassed / this.stats.totalAttempts) * 100);
  } else {
    const totalPassed = Math.round((this.stats.passRate / 100) * (this.stats.totalAttempts - 1));
    this.stats.passRate = Math.round((totalPassed / this.stats.totalAttempts) * 100);
  }
  
  // Update highest and lowest scores
  if (score > this.stats.highestScore) {
    this.stats.highestScore = score;
  }
  if (score < this.stats.lowestScore) {
    this.stats.lowestScore = score;
  }
};

// Method to generate test questions
testSchema.methods.generateTestQuestions = async function() {
  const Question = mongoose.model('Question');
  
  // Get questions from the same category
  const availableQuestions = await Question.find({
    category: this.category,
    tier: this.tier === 'premium' ? { $in: ['free', 'premium'] } : 'free',
    isActive: true
  });
  
  if (availableQuestions.length < this.settings.questionCount) {
    throw new Error('Not enough questions available for this test');
  }
  
  // Shuffle and select required number of questions
  const shuffled = availableQuestions.sort(() => 0.5 - Math.random());
  this.questions = shuffled.slice(0, this.settings.questionCount).map(q => q._id);
  
  return this.questions;
};

// Method to get test for user
testSchema.methods.getTestForUser = function(includeQuestions = false) {
  const testObj = this.toObject();
  
  if (!includeQuestions) {
    delete testObj.questions;
  }
  
  return testObj;
};

// Static method to get published tests
testSchema.statics.getPublishedTests = function(filters = {}, page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  const baseFilters = { isActive: true, isPublished: true, ...filters };
  
  return this.find(baseFilters)
    .populate('category', 'name slug color')
    .populate('createdBy', 'name')
    .select('-questions')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get tests by category
testSchema.statics.getTestsByCategory = function(categoryId, tier = null, limit = null) {
  const filters = { 
    category: categoryId, 
    isActive: true, 
    isPublished: true 
  };
  if (tier) filters.tier = tier;
  
  let query = this.find(filters)
    .populate('category', 'name slug color')
    .select('-questions')
    .sort({ createdAt: -1 });
  
  if (limit) query = query.limit(limit);
  
  return query;
};

module.exports = mongoose.model('Test', testSchema);
