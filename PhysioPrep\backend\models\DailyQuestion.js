const mongoose = require('mongoose');

const dailyQuestionSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: [true, 'Date is required'],
    unique: true,
    index: true
  },
  question: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Question',
    required: [true, 'Question is required']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  tier: {
    type: String,
    enum: ['free', 'premium'],
    required: [true, 'Tier is required']
  },
  stats: {
    totalAttempts: {
      type: Number,
      default: 0
    },
    correctAttempts: {
      type: Number,
      default: 0
    },
    averageTime: {
      type: Number,
      default: 0
    },
    uniqueUsers: {
      type: Number,
      default: 0
    }
  },
  userAttempts: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    selectedAnswer: {
      type: Number,
      required: true
    },
    isCorrect: {
      type: Boolean,
      required: true
    },
    timeSpent: {
      type: Number,
      default: 0
    },
    attemptedAt: {
      type: Date,
      default: Date.now
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for success rate
dailyQuestionSchema.virtual('successRate').get(function() {
  if (this.stats.totalAttempts === 0) return 0;
  return Math.round((this.stats.correctAttempts / this.stats.totalAttempts) * 100);
});

// Virtual for formatted date
dailyQuestionSchema.virtual('formattedDate').get(function() {
  return this.date.toISOString().split('T')[0];
});

// Indexes
dailyQuestionSchema.index({ date: -1 });
dailyQuestionSchema.index({ isActive: 1, date: -1 });
dailyQuestionSchema.index({ 'userAttempts.user': 1 });

// Method to add user attempt
dailyQuestionSchema.methods.addUserAttempt = function(userId, selectedAnswer, isCorrect, timeSpent = 0) {
  // Check if user already attempted
  const existingAttempt = this.userAttempts.find(attempt => 
    attempt.user.toString() === userId.toString()
  );
  
  if (existingAttempt) {
    throw new Error('User has already attempted this daily question');
  }
  
  // Add new attempt
  this.userAttempts.push({
    user: userId,
    selectedAnswer,
    isCorrect,
    timeSpent,
    attemptedAt: new Date()
  });
  
  // Update stats
  this.stats.totalAttempts += 1;
  if (isCorrect) {
    this.stats.correctAttempts += 1;
  }
  
  // Update average time
  const totalTime = this.stats.averageTime * (this.stats.totalAttempts - 1);
  this.stats.averageTime = Math.round((totalTime + timeSpent) / this.stats.totalAttempts);
  
  // Update unique users count
  this.stats.uniqueUsers = this.userAttempts.length;
  
  return this;
};

// Method to check if user has attempted
dailyQuestionSchema.methods.hasUserAttempted = function(userId) {
  return this.userAttempts.some(attempt => 
    attempt.user.toString() === userId.toString()
  );
};

// Method to get user attempt
dailyQuestionSchema.methods.getUserAttempt = function(userId) {
  return this.userAttempts.find(attempt => 
    attempt.user.toString() === userId.toString()
  );
};

// Method to get question for user (without correct answer)
dailyQuestionSchema.methods.getQuestionForUser = function(userId) {
  const hasAttempted = this.hasUserAttempted(userId);
  const userAttempt = hasAttempted ? this.getUserAttempt(userId) : null;
  
  return {
    _id: this._id,
    date: this.date,
    formattedDate: this.formattedDate,
    category: this.category,
    tier: this.tier,
    question: this.question,
    stats: {
      totalAttempts: this.stats.totalAttempts,
      successRate: this.successRate
    },
    hasAttempted,
    userAttempt: userAttempt ? {
      selectedAnswer: userAttempt.selectedAnswer,
      isCorrect: userAttempt.isCorrect,
      timeSpent: userAttempt.timeSpent,
      attemptedAt: userAttempt.attemptedAt
    } : null
  };
};

// Static method to get today's question
dailyQuestionSchema.statics.getTodaysQuestion = function() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return this.findOne({ 
    date: today,
    isActive: true 
  })
  .populate('question')
  .populate('category', 'name slug color');
};

// Static method to get question by date
dailyQuestionSchema.statics.getQuestionByDate = function(date) {
  const queryDate = new Date(date);
  queryDate.setHours(0, 0, 0, 0);
  
  return this.findOne({ 
    date: queryDate,
    isActive: true 
  })
  .populate('question')
  .populate('category', 'name slug color');
};

// Static method to get recent daily questions
dailyQuestionSchema.statics.getRecentQuestions = function(limit = 7) {
  return this.find({ isActive: true })
    .populate('question', 'question options difficulty')
    .populate('category', 'name slug color')
    .sort({ date: -1 })
    .limit(limit);
};

// Static method to create daily question
dailyQuestionSchema.statics.createDailyQuestion = async function(date, questionId, createdBy = null) {
  const Question = mongoose.model('Question');
  
  // Get question details
  const question = await Question.findById(questionId).populate('category');
  if (!question) {
    throw new Error('Question not found');
  }
  
  // Check if daily question already exists for this date
  const existingDailyQuestion = await this.findOne({ date });
  if (existingDailyQuestion) {
    throw new Error('Daily question already exists for this date');
  }
  
  // Create new daily question
  const dailyQuestion = new this({
    date,
    question: questionId,
    category: question.category._id,
    tier: question.tier,
    createdBy
  });
  
  return await dailyQuestion.save();
};

// Static method to get user's daily question history
dailyQuestionSchema.statics.getUserHistory = function(userId, page = 1, limit = 30) {
  const skip = (page - 1) * limit;
  
  return this.aggregate([
    {
      $match: {
        isActive: true,
        'userAttempts.user': mongoose.Types.ObjectId(userId)
      }
    },
    {
      $addFields: {
        userAttempt: {
          $arrayElemAt: [
            {
              $filter: {
                input: '$userAttempts',
                cond: { $eq: ['$$this.user', mongoose.Types.ObjectId(userId)] }
              }
            },
            0
          ]
        }
      }
    },
    {
      $lookup: {
        from: 'questions',
        localField: 'question',
        foreignField: '_id',
        as: 'questionDetails'
      }
    },
    {
      $lookup: {
        from: 'categories',
        localField: 'category',
        foreignField: '_id',
        as: 'categoryDetails'
      }
    },
    {
      $project: {
        date: 1,
        tier: 1,
        stats: 1,
        userAttempt: 1,
        question: { $arrayElemAt: ['$questionDetails', 0] },
        category: { $arrayElemAt: ['$categoryDetails', 0] }
      }
    },
    { $sort: { date: -1 } },
    { $skip: skip },
    { $limit: limit }
  ]);
};

module.exports = mongoose.model('DailyQuestion', dailyQuestionSchema);
