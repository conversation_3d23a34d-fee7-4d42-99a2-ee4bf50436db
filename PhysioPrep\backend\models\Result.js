const mongoose = require('mongoose');

const resultSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  type: {
    type: String,
    enum: ['quiz', 'test', 'daily_question'],
    required: [true, 'Result type is required']
  },
  quiz: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Quiz',
    default: null
  },
  test: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Test',
    default: null
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  questions: [{
    question: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
      required: true
    },
    selectedAnswer: {
      type: Number,
      required: true // Index of selected option
    },
    correctAnswer: {
      type: Number,
      required: true // Index of correct option
    },
    isCorrect: {
      type: Boolean,
      required: true
    },
    timeSpent: {
      type: Number,
      default: 0 // in seconds
    },
    points: {
      type: Number,
      default: 1
    }
  }],
  score: {
    total: {
      type: Number,
      required: true
    },
    percentage: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    },
    correct: {
      type: Number,
      required: true
    },
    incorrect: {
      type: Number,
      required: true
    },
    unanswered: {
      type: Number,
      default: 0
    }
  },
  timing: {
    startTime: {
      type: Date,
      required: true
    },
    endTime: {
      type: Date,
      required: true
    },
    totalTime: {
      type: Number,
      required: true // in seconds
    },
    timeLimit: {
      type: Number,
      default: null // in seconds
    }
  },
  status: {
    type: String,
    enum: ['completed', 'abandoned', 'timeout'],
    default: 'completed'
  },
  passed: {
    type: Boolean,
    required: true
  },
  passingScore: {
    type: Number,
    default: 70
  },
  feedback: {
    strengths: [{
      type: String
    }],
    improvements: [{
      type: String
    }],
    recommendations: [{
      type: String
    }]
  },
  analytics: {
    averageTimePerQuestion: {
      type: Number,
      default: 0
    },
    fastestQuestion: {
      time: {
        type: Number,
        default: 0
      },
      questionId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Question',
        default: null
      }
    },
    slowestQuestion: {
      time: {
        type: Number,
        default: 0
      },
      questionId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Question',
        default: null
      }
    },
    difficultyBreakdown: {
      easy: {
        total: { type: Number, default: 0 },
        correct: { type: Number, default: 0 }
      },
      medium: {
        total: { type: Number, default: 0 },
        correct: { type: Number, default: 0 }
      },
      hard: {
        total: { type: Number, default: 0 },
        correct: { type: Number, default: 0 }
      }
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for grade
resultSchema.virtual('grade').get(function() {
  const percentage = this.score.percentage;
  if (percentage >= 90) return 'A+';
  if (percentage >= 85) return 'A';
  if (percentage >= 80) return 'B+';
  if (percentage >= 75) return 'B';
  if (percentage >= 70) return 'C+';
  if (percentage >= 65) return 'C';
  if (percentage >= 60) return 'D+';
  if (percentage >= 55) return 'D';
  return 'F';
});

// Virtual for performance level
resultSchema.virtual('performanceLevel').get(function() {
  const percentage = this.score.percentage;
  if (percentage >= 85) return 'Excellent';
  if (percentage >= 75) return 'Good';
  if (percentage >= 65) return 'Average';
  if (percentage >= 50) return 'Below Average';
  return 'Poor';
});

// Indexes
resultSchema.index({ user: 1, type: 1, createdAt: -1 });
resultSchema.index({ category: 1, createdAt: -1 });
resultSchema.index({ quiz: 1, createdAt: -1 });
resultSchema.index({ test: 1, createdAt: -1 });
resultSchema.index({ 'score.percentage': -1 });

// Pre-save middleware to calculate analytics
resultSchema.pre('save', function(next) {
  if (this.isNew) {
    this.calculateAnalytics();
  }
  next();
});

// Method to calculate analytics
resultSchema.methods.calculateAnalytics = function() {
  if (this.questions.length === 0) return;
  
  // Calculate average time per question
  const totalTime = this.questions.reduce((sum, q) => sum + q.timeSpent, 0);
  this.analytics.averageTimePerQuestion = Math.round(totalTime / this.questions.length);
  
  // Find fastest and slowest questions
  let fastest = this.questions[0];
  let slowest = this.questions[0];
  
  this.questions.forEach(q => {
    if (q.timeSpent < fastest.timeSpent) fastest = q;
    if (q.timeSpent > slowest.timeSpent) slowest = q;
  });
  
  this.analytics.fastestQuestion = {
    time: fastest.timeSpent,
    questionId: fastest.question
  };
  
  this.analytics.slowestQuestion = {
    time: slowest.timeSpent,
    questionId: slowest.question
  };
};

// Method to generate feedback
resultSchema.methods.generateFeedback = async function() {
  const Question = mongoose.model('Question');
  
  // Get question details for analysis
  const questionIds = this.questions.map(q => q.question);
  const questionDetails = await Question.find({ _id: { $in: questionIds } })
    .select('difficulty tags');
  
  const feedback = {
    strengths: [],
    improvements: [],
    recommendations: []
  };
  
  // Analyze performance by difficulty
  const difficultyStats = {};
  this.questions.forEach((q, index) => {
    const questionDetail = questionDetails.find(qd => qd._id.toString() === q.question.toString());
    if (questionDetail) {
      const difficulty = questionDetail.difficulty;
      if (!difficultyStats[difficulty]) {
        difficultyStats[difficulty] = { total: 0, correct: 0 };
      }
      difficultyStats[difficulty].total++;
      if (q.isCorrect) difficultyStats[difficulty].correct++;
    }
  });
  
  // Generate feedback based on performance
  Object.keys(difficultyStats).forEach(difficulty => {
    const stats = difficultyStats[difficulty];
    const percentage = (stats.correct / stats.total) * 100;
    
    if (percentage >= 80) {
      feedback.strengths.push(`Strong performance in ${difficulty} questions (${percentage.toFixed(1)}%)`);
    } else if (percentage < 60) {
      feedback.improvements.push(`Need improvement in ${difficulty} questions (${percentage.toFixed(1)}%)`);
      feedback.recommendations.push(`Practice more ${difficulty} level questions`);
    }
  });
  
  // Overall performance feedback
  if (this.score.percentage >= 85) {
    feedback.strengths.push('Excellent overall performance');
  } else if (this.score.percentage >= 70) {
    feedback.strengths.push('Good understanding of the concepts');
  } else {
    feedback.improvements.push('Overall performance needs improvement');
    feedback.recommendations.push('Review fundamental concepts and practice more');
  }
  
  // Time management feedback
  if (this.timing.timeLimit) {
    const timeUsedPercentage = (this.timing.totalTime / this.timing.timeLimit) * 100;
    if (timeUsedPercentage < 70) {
      feedback.strengths.push('Good time management');
    } else if (timeUsedPercentage > 95) {
      feedback.improvements.push('Time management needs improvement');
      feedback.recommendations.push('Practice answering questions more quickly');
    }
  }
  
  this.feedback = feedback;
  return feedback;
};

// Static method to get user results
resultSchema.statics.getUserResults = function(userId, type = null, page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  const filters = { user: userId };
  if (type) filters.type = type;
  
  return this.find(filters)
    .populate('category', 'name slug color')
    .populate('quiz', 'title')
    .populate('test', 'title')
    .select('-questions')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get category performance
resultSchema.statics.getCategoryPerformance = function(userId, categoryId) {
  return this.aggregate([
    { $match: { user: mongoose.Types.ObjectId(userId), category: mongoose.Types.ObjectId(categoryId) } },
    {
      $group: {
        _id: '$category',
        totalAttempts: { $sum: 1 },
        averageScore: { $avg: '$score.percentage' },
        bestScore: { $max: '$score.percentage' },
        totalTime: { $sum: '$timing.totalTime' },
        passedCount: { $sum: { $cond: ['$passed', 1, 0] } }
      }
    },
    {
      $addFields: {
        passRate: { $multiply: [{ $divide: ['$passedCount', '$totalAttempts'] }, 100] },
        averageTime: { $divide: ['$totalTime', '$totalAttempts'] }
      }
    }
  ]);
};

module.exports = mongoose.model('Result', resultSchema);
