const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', err);

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = {
      message,
      statusCode: 404
    };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    let message = 'Duplicate field value entered';
    
    // Extract field name from error
    const field = Object.keys(err.keyValue)[0];
    if (field === 'email') {
      message = 'Email address is already registered';
    } else if (field === 'name') {
      message = 'Name already exists';
    } else if (field === 'slug') {
      message = 'URL slug already exists';
    }
    
    error = {
      message,
      statusCode: 400
    };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = {
      message,
      statusCode: 400
    };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = {
      message: 'Invalid token',
      statusCode: 401
    };
  }

  if (err.name === 'TokenExpiredError') {
    error = {
      message: 'Token expired',
      statusCode: 401
    };
  }

  // File upload errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    error = {
      message: 'File size too large',
      statusCode: 400
    };
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    error = {
      message: 'Too many files uploaded',
      statusCode: 400
    };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    error = {
      message: 'Unexpected file field',
      statusCode: 400
    };
  }

  // Express validator errors
  if (err.type === 'validation') {
    error = {
      message: err.message || 'Validation failed',
      statusCode: 400,
      errors: err.errors
    };
  }

  // Database connection errors
  if (err.name === 'MongoNetworkError') {
    error = {
      message: 'Database connection failed',
      statusCode: 500
    };
  }

  if (err.name === 'MongoTimeoutError') {
    error = {
      message: 'Database operation timed out',
      statusCode: 500
    };
  }

  // Payment errors
  if (err.type === 'StripeCardError') {
    error = {
      message: err.message || 'Payment failed',
      statusCode: 400
    };
  }

  if (err.type === 'StripeInvalidRequestError') {
    error = {
      message: 'Invalid payment request',
      statusCode: 400
    };
  }

  // Email errors
  if (err.code === 'EAUTH' || err.code === 'ECONNECTION') {
    error = {
      message: 'Email service unavailable',
      statusCode: 500
    };
  }

  // Rate limiting errors
  if (err.status === 429) {
    error = {
      message: 'Too many requests, please try again later',
      statusCode: 429
    };
  }

  // Default error response
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Server Error';

  const response = {
    success: false,
    message
  };

  // Add error details in development
  if (process.env.NODE_ENV === 'development') {
    response.error = err;
    response.stack = err.stack;
  }

  // Add validation errors if present
  if (error.errors) {
    response.errors = error.errors;
  }

  // Add specific error codes for client handling
  if (err.code) {
    response.code = err.code;
  }

  res.status(statusCode).json(response);
};

// Async error handler wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Custom error class
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Not found middleware
const notFound = (req, res, next) => {
  const error = new AppError(`Not found - ${req.originalUrl}`, 404);
  next(error);
};

module.exports = {
  errorHandler,
  asyncHandler,
  AppError,
  notFound
};
