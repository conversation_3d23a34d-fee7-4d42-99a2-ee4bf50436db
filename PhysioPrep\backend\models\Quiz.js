const mongoose = require('mongoose');

const quizSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Quiz title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  questions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Question'
  }],
  tier: {
    type: String,
    enum: ['free', 'premium'],
    required: [true, 'Quiz tier is required'],
    default: 'free'
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard', 'mixed'],
    default: 'mixed'
  },
  settings: {
    timeLimit: {
      type: Number,
      default: null // in minutes, null means no time limit
    },
    showCorrectAnswers: {
      type: Boolean,
      default: true
    },
    showExplanations: {
      type: Boolean,
      default: true
    },
    randomizeQuestions: {
      type: Boolean,
      default: false
    },
    randomizeOptions: {
      type: Boolean,
      default: false
    },
    allowReview: {
      type: Boolean,
      default: true
    },
    passingScore: {
      type: Number,
      default: 70,
      min: 0,
      max: 100
    }
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  image: {
    url: {
      type: String,
      default: null
    },
    alt: {
      type: String,
      default: null
    }
  },
  stats: {
    totalAttempts: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },
    averageTime: {
      type: Number,
      default: 0
    },
    passRate: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPublished: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for question count
quizSchema.virtual('questionCount').get(function() {
  return this.questions.length;
});

// Virtual for estimated duration
quizSchema.virtual('estimatedDuration').get(function() {
  if (this.settings.timeLimit) {
    return this.settings.timeLimit;
  }
  // Estimate 1.5 minutes per question
  return Math.ceil(this.questions.length * 1.5);
});

// Indexes
quizSchema.index({ category: 1, tier: 1, isActive: 1, isPublished: 1 });
quizSchema.index({ difficulty: 1, isActive: 1 });
quizSchema.index({ tags: 1 });
quizSchema.index({ createdAt: -1 });

// Validation: Ensure quiz has questions
quizSchema.pre('save', function(next) {
  if (this.isPublished && this.questions.length === 0) {
    return next(new Error('Published quiz must have at least one question'));
  }
  next();
});

// Method to update stats
quizSchema.methods.updateStats = function(score, timeSpent, passed) {
  this.stats.totalAttempts += 1;
  
  // Update average score
  const totalScore = this.stats.averageScore * (this.stats.totalAttempts - 1);
  this.stats.averageScore = Math.round((totalScore + score) / this.stats.totalAttempts);
  
  // Update average time
  const totalTime = this.stats.averageTime * (this.stats.totalAttempts - 1);
  this.stats.averageTime = Math.round((totalTime + timeSpent) / this.stats.totalAttempts);
  
  // Update pass rate
  if (passed) {
    const totalPassed = Math.round((this.stats.passRate / 100) * (this.stats.totalAttempts - 1)) + 1;
    this.stats.passRate = Math.round((totalPassed / this.stats.totalAttempts) * 100);
  } else {
    const totalPassed = Math.round((this.stats.passRate / 100) * (this.stats.totalAttempts - 1));
    this.stats.passRate = Math.round((totalPassed / this.stats.totalAttempts) * 100);
  }
};

// Method to get quiz for user
quizSchema.methods.getQuizForUser = function(includeQuestions = false) {
  const quizObj = this.toObject();
  
  if (!includeQuestions) {
    delete quizObj.questions;
  }
  
  return quizObj;
};

// Static method to get published quizzes
quizSchema.statics.getPublishedQuizzes = function(filters = {}, page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  const baseFilters = { isActive: true, isPublished: true, ...filters };
  
  return this.find(baseFilters)
    .populate('category', 'name slug color')
    .populate('createdBy', 'name')
    .select('-questions')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get quiz by category
quizSchema.statics.getQuizzesByCategory = function(categoryId, tier = null, limit = null) {
  const filters = { 
    category: categoryId, 
    isActive: true, 
    isPublished: true 
  };
  if (tier) filters.tier = tier;
  
  let query = this.find(filters)
    .populate('category', 'name slug color')
    .select('-questions')
    .sort({ createdAt: -1 });
  
  if (limit) query = query.limit(limit);
  
  return query;
};

// Static method to get random quiz
quizSchema.statics.getRandomQuiz = function(filters = {}) {
  const pipeline = [
    { $match: { isActive: true, isPublished: true, ...filters } },
    { $sample: { size: 1 } },
    {
      $lookup: {
        from: 'categories',
        localField: 'category',
        foreignField: '_id',
        as: 'categoryInfo'
      }
    },
    {
      $project: {
        title: 1,
        description: 1,
        category: 1,
        tier: 1,
        difficulty: 1,
        settings: 1,
        tags: 1,
        image: 1,
        stats: 1,
        questionCount: { $size: '$questions' },
        categoryInfo: { $arrayElemAt: ['$categoryInfo', 0] }
      }
    }
  ];
  
  return this.aggregate(pipeline);
};

module.exports = mongoose.model('Quiz', quizSchema);
