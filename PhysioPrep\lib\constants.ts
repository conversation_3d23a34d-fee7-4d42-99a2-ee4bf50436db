import { Theme } from '@react-navigation/native';

// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:5000/api' : 'https://your-production-api.com/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
};

// App Configuration
export const APP_CONFIG = {
  NAME: 'PhysioPrep',
  VERSION: '1.0.0',
  SUPPORT_EMAIL: '<EMAIL>',
  PRIVACY_URL: 'https://physioprep.com/privacy',
  TERMS_URL: 'https://physioprep.com/terms',
};

// Color Palette
export const COLORS = {
  primary: {
    100: '#FF6B6B',
    200: '#dd4d51',
    300: '#8f001a',
  },
  accent: {
    100: '#00FFFF',
    200: '#00999b',
  },
  text: {
    100: '#FFFFFF',
    200: '#e0e0e0',
  },
  bg: {
    100: '#0F0F0F',
    200: '#1f1f1f',
    300: '#353535',
  },
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
};

// Navigation Theme
export const NAV_THEME: Theme = {
  dark: true,
  colors: {
    primary: COLORS.primary[100],
    background: COLORS.bg[100],
    card: COLORS.bg[200],
    text: COLORS.text[100],
    border: COLORS.bg[300],
    notification: COLORS.primary[100],
  },
};

// Question Types
export const QUESTION_TYPES = {
  MULTIPLE_CHOICE: 'multiple_choice',
} as const;

// Question Difficulties
export const DIFFICULTIES = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard',
} as const;

// Question Tiers
export const TIERS = {
  FREE: 'free',
  PREMIUM: 'premium',
} as const;

// Quiz/Test Types
export const ASSESSMENT_TYPES = {
  QUIZ: 'quiz',
  TEST: 'test',
  DAILY_QUESTION: 'daily_question',
} as const;

// User Roles
export const USER_ROLES = {
  USER: 'user',
  ADMIN: 'admin',
} as const;

// Subscription Types
export const SUBSCRIPTION_TYPES = {
  FREE: 'free',
  PREMIUM: 'premium',
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  THEME: 'theme',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  LAST_DAILY_QUESTION: 'last_daily_question',
  QUIZ_PROGRESS: 'quiz_progress',
  TEST_PROGRESS: 'test_progress',
  SETTINGS: 'settings',
} as const;

// Animation Durations
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Screen Dimensions
export const SCREEN_PADDING = {
  HORIZONTAL: 20,
  VERTICAL: 16,
} as const;

// Quiz/Test Settings
export const QUIZ_SETTINGS = {
  DEFAULT_TIME_LIMIT: null, // No time limit
  MIN_QUESTIONS: 5,
  MAX_QUESTIONS: 50,
  DEFAULT_PASSING_SCORE: 70,
} as const;

export const TEST_SETTINGS = {
  DEFAULT_TIME_LIMIT: 60, // 60 minutes
  MIN_QUESTIONS: 10,
  MAX_QUESTIONS: 100,
  DEFAULT_PASSING_SCORE: 70,
  MIN_TIME_LIMIT: 5,
  MAX_TIME_LIMIT: 300,
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
} as const;

// File Upload
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'text/plain'],
} as const;

// Validation Rules
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 6,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_MAX_LENGTH: 255,
  QUESTION_MAX_LENGTH: 1000,
  OPTION_MAX_LENGTH: 500,
  EXPLANATION_MAX_LENGTH: 2000,
  CATEGORY_NAME_MAX_LENGTH: 100,
  CATEGORY_DESCRIPTION_MAX_LENGTH: 500,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  PREMIUM_REQUIRED: 'This feature requires a premium subscription.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Welcome back!',
  REGISTER_SUCCESS: 'Account created successfully!',
  LOGOUT_SUCCESS: 'Logged out successfully.',
  PROFILE_UPDATED: 'Profile updated successfully.',
  PASSWORD_CHANGED: 'Password changed successfully.',
  QUIZ_COMPLETED: 'Quiz completed successfully!',
  TEST_COMPLETED: 'Test completed successfully!',
  DAILY_QUESTION_COMPLETED: 'Daily question completed!',
} as const;

// Feature Flags
export const FEATURES = {
  DAILY_QUESTIONS: true,
  PREMIUM_CONTENT: true,
  PUSH_NOTIFICATIONS: true,
  OFFLINE_MODE: false,
  ANALYTICS: true,
  SOCIAL_SHARING: true,
} as const;

// Social Media
export const SOCIAL_LINKS = {
  FACEBOOK: 'https://facebook.com/physioprep',
  TWITTER: 'https://twitter.com/physioprep',
  INSTAGRAM: 'https://instagram.com/physioprep',
  LINKEDIN: 'https://linkedin.com/company/physioprep',
} as const;

// Grade Thresholds
export const GRADE_THRESHOLDS = {
  'A+': 90,
  'A': 85,
  'B+': 80,
  'B': 75,
  'C+': 70,
  'C': 65,
  'D+': 60,
  'D': 55,
  'F': 0,
} as const;

// Performance Levels
export const PERFORMANCE_LEVELS = {
  EXCELLENT: { min: 85, color: COLORS.success },
  GOOD: { min: 75, color: COLORS.info },
  AVERAGE: { min: 65, color: COLORS.warning },
  BELOW_AVERAGE: { min: 50, color: COLORS.warning },
  POOR: { min: 0, color: COLORS.error },
} as const;

// Time Limits (in minutes)
export const TIME_LIMITS = {
  DAILY_QUESTION: null,
  SHORT_QUIZ: 15,
  MEDIUM_QUIZ: 30,
  LONG_QUIZ: 60,
  SHORT_TEST: 30,
  MEDIUM_TEST: 60,
  LONG_TEST: 120,
} as const;
