{"name": "find-babel-config", "version": "2.1.2", "main": "src/index.js", "description": "Find the closest babel config based on a directory", "repository": {"type": "git", "url": "https://github.com/tleunen/find-babel-config.git"}, "author": "<PERSON> <<EMAIL>> (http://tommyleunen.com)", "license": "MIT", "keywords": ["babel", "config", "loader", "finder", "babelrc"], "dependencies": {"json5": "^2.2.3"}, "devDependencies": {"@babel/cli": "^7.24.1", "@babel/preset-env": "^7.24.4", "eslint": "^6.7.2", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.19.1", "jest": "^29.7.0", "standard-version": "^9.5.0"}, "scripts": {"lint": "eslint src test", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "test": "jest", "release": "standard-version"}, "jest": {"testRegex": "/test/.*\\.test\\.js$", "collectCoverageFrom": ["src/**/*.js"]}, "greenkeeper": {"ignore": ["eslint", "eslint-plugin-import", "babel-jest"]}}