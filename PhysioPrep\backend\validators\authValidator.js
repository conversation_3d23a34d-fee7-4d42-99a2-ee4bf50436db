const { validationResult } = require('express-validator');

// Middleware to validate request and return errors
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));
    
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: formattedErrors
    });
  }
  
  next();
};

// Custom validators
const customValidators = {
  // Check if email is not already taken
  isEmailAvailable: async (email, { req }) => {
    const User = require('../models/User');
    const existingUser = await User.findOne({ email });
    
    // If updating profile, allow same email for current user
    if (req.user && existingUser && existingUser._id.toString() === req.user._id.toString()) {
      return true;
    }
    
    if (existingUser) {
      throw new Error('Email is already registered');
    }
    
    return true;
  },
  
  // Check if category name is not already taken
  isCategoryNameAvailable: async (name, { req }) => {
    const Category = require('../models/Category');
    const existingCategory = await Category.findOne({ name });
    
    // If updating category, allow same name for current category
    if (req.params.id && existingCategory && existingCategory._id.toString() === req.params.id) {
      return true;
    }
    
    if (existingCategory) {
      throw new Error('Category name already exists');
    }
    
    return true;
  },
  
  // Check if category slug is not already taken
  isCategorySlugAvailable: async (slug, { req }) => {
    const Category = require('../models/Category');
    const existingCategory = await Category.findOne({ slug });
    
    // If updating category, allow same slug for current category
    if (req.params.id && existingCategory && existingCategory._id.toString() === req.params.id) {
      return true;
    }
    
    if (existingCategory) {
      throw new Error('Category slug already exists');
    }
    
    return true;
  },
  
  // Validate ObjectId
  isValidObjectId: (value) => {
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(value)) {
      throw new Error('Invalid ID format');
    }
    return true;
  },
  
  // Check if category exists
  categoryExists: async (categoryId) => {
    const Category = require('../models/Category');
    const category = await Category.findById(categoryId);
    
    if (!category) {
      throw new Error('Category not found');
    }
    
    return true;
  },
  
  // Check if question exists
  questionExists: async (questionId) => {
    const Question = require('../models/Question');
    const question = await Question.findById(questionId);
    
    if (!question) {
      throw new Error('Question not found');
    }
    
    return true;
  },
  
  // Validate question options
  validateQuestionOptions: (options) => {
    if (!Array.isArray(options)) {
      throw new Error('Options must be an array');
    }
    
    if (options.length < 2 || options.length > 6) {
      throw new Error('Question must have between 2 and 6 options');
    }
    
    const correctAnswers = options.filter(option => option.isCorrect);
    if (correctAnswers.length !== 1) {
      throw new Error('Question must have exactly one correct answer');
    }
    
    // Check for duplicate option texts
    const optionTexts = options.map(option => option.text.toLowerCase().trim());
    const uniqueTexts = [...new Set(optionTexts)];
    if (optionTexts.length !== uniqueTexts.length) {
      throw new Error('Option texts must be unique');
    }
    
    return true;
  },
  
  // Validate hex color
  isValidHexColor: (color) => {
    const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (!hexColorRegex.test(color)) {
      throw new Error('Please provide a valid hex color code');
    }
    return true;
  },
  
  // Validate date is not in the future
  isNotFutureDate: (date) => {
    const inputDate = new Date(date);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today
    
    if (inputDate > today) {
      throw new Error('Date cannot be in the future');
    }
    
    return true;
  },
  
  // Validate age (for date of birth)
  isValidAge: (dateOfBirth) => {
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    if (age < 13 || age > 120) {
      throw new Error('Age must be between 13 and 120 years');
    }
    
    return true;
  },
  
  // Validate password strength
  isStrongPassword: (password) => {
    const minLength = 6;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    if (password.length < minLength) {
      throw new Error(`Password must be at least ${minLength} characters long`);
    }
    
    if (!hasUpperCase) {
      throw new Error('Password must contain at least one uppercase letter');
    }
    
    if (!hasLowerCase) {
      throw new Error('Password must contain at least one lowercase letter');
    }
    
    if (!hasNumbers) {
      throw new Error('Password must contain at least one number');
    }
    
    // Optional: require special characters for stronger passwords
    // if (!hasSpecialChar) {
    //   throw new Error('Password must contain at least one special character');
    // }
    
    return true;
  }
};

module.exports = {
  validateRequest,
  customValidators
};
