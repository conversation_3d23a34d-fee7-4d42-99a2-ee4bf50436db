const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Category name is required'],
    unique: true,
    trim: true,
    maxlength: [100, 'Category name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Category description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  icon: {
    type: String,
    default: null
  },
  color: {
    type: String,
    default: '#FF6B6B',
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Please enter a valid hex color']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  stats: {
    totalQuestions: {
      type: Number,
      default: 0
    },
    freeQuestions: {
      type: Number,
      default: 0
    },
    premiumQuestions: {
      type: Number,
      default: 0
    },
    totalQuizzes: {
      type: Number,
      default: 0
    },
    totalTests: {
      type: Number,
      default: 0
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for question count by tier
categorySchema.virtual('questionsByTier').get(function() {
  return {
    free: this.stats.freeQuestions,
    premium: this.stats.premiumQuestions,
    total: this.stats.totalQuestions
  };
});

// Index for slug lookup
categorySchema.index({ slug: 1 });
categorySchema.index({ isActive: 1, order: 1 });

// Pre-save middleware to generate slug
categorySchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Method to update question stats
categorySchema.methods.updateQuestionStats = function(tier, increment = true) {
  const change = increment ? 1 : -1;
  
  this.stats.totalQuestions += change;
  
  if (tier === 'free') {
    this.stats.freeQuestions += change;
  } else if (tier === 'premium') {
    this.stats.premiumQuestions += change;
  }
  
  // Ensure stats don't go below 0
  this.stats.totalQuestions = Math.max(0, this.stats.totalQuestions);
  this.stats.freeQuestions = Math.max(0, this.stats.freeQuestions);
  this.stats.premiumQuestions = Math.max(0, this.stats.premiumQuestions);
};

// Method to update quiz stats
categorySchema.methods.updateQuizStats = function(increment = true) {
  const change = increment ? 1 : -1;
  this.stats.totalQuizzes += change;
  this.stats.totalQuizzes = Math.max(0, this.stats.totalQuizzes);
};

// Method to update test stats
categorySchema.methods.updateTestStats = function(increment = true) {
  const change = increment ? 1 : -1;
  this.stats.totalTests += change;
  this.stats.totalTests = Math.max(0, this.stats.totalTests);
};

// Static method to get active categories
categorySchema.statics.getActiveCategories = function() {
  return this.find({ isActive: true }).sort({ order: 1, name: 1 });
};

// Static method to get category with stats
categorySchema.statics.getCategoryWithStats = function(slug) {
  return this.findOne({ slug, isActive: true })
    .populate('createdBy', 'name email')
    .lean();
};

module.exports = mongoose.model('Category', categorySchema);
