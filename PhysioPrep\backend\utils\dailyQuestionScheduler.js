const DailyQuestion = require('../models/DailyQuestion');
const Question = require('../models/Question');
const Category = require('../models/Category');

// Update daily question for today
const updateDailyQuestion = async () => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Check if today's question already exists
    const existingDailyQuestion = await DailyQuestion.findOne({ date: today });
    
    if (existingDailyQuestion) {
      console.log('Daily question already exists for today');
      return existingDailyQuestion;
    }
    
    // Get a random question for today
    const selectedQuestion = await selectDailyQuestion();
    
    if (!selectedQuestion) {
      console.error('No suitable question found for daily question');
      return null;
    }
    
    // Create new daily question
    const dailyQuestion = new DailyQuestion({
      date: today,
      question: selectedQuestion._id,
      category: selectedQuestion.category,
      tier: selectedQuestion.tier
    });
    
    await dailyQuestion.save();
    
    console.log(`Daily question created for ${today.toDateString()}: ${selectedQuestion.question.substring(0, 50)}...`);
    return dailyQuestion;
    
  } catch (error) {
    console.error('Error updating daily question:', error);
    throw error;
  }
};

// Select a question for daily question
const selectDailyQuestion = async () => {
  try {
    // Get all active categories
    const categories = await Category.find({ isActive: true });
    
    if (categories.length === 0) {
      throw new Error('No active categories found');
    }
    
    // Get questions that haven't been used as daily questions recently (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentDailyQuestions = await DailyQuestion.find({
      date: { $gte: thirtyDaysAgo }
    }).select('question');
    
    const recentQuestionIds = recentDailyQuestions.map(dq => dq.question);
    
    // Strategy: Alternate between free and premium questions
    // Check what was yesterday's tier
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    
    const yesterdayQuestion = await DailyQuestion.findOne({ date: yesterday });
    let preferredTier = 'free'; // Default to free
    
    if (yesterdayQuestion) {
      // Alternate tier
      preferredTier = yesterdayQuestion.tier === 'free' ? 'premium' : 'free';
    }
    
    // Try to find a question with preferred tier first
    let selectedQuestion = await findQuestionByTier(preferredTier, recentQuestionIds);
    
    // If no question found with preferred tier, try the other tier
    if (!selectedQuestion) {
      const alternateTier = preferredTier === 'free' ? 'premium' : 'free';
      selectedQuestion = await findQuestionByTier(alternateTier, recentQuestionIds);
    }
    
    // If still no question found, get any available question
    if (!selectedQuestion) {
      selectedQuestion = await findQuestionByTier(null, recentQuestionIds);
    }
    
    return selectedQuestion;
    
  } catch (error) {
    console.error('Error selecting daily question:', error);
    throw error;
  }
};

// Find question by tier
const findQuestionByTier = async (tier, excludeIds = []) => {
  try {
    const query = {
      isActive: true,
      _id: { $nin: excludeIds }
    };
    
    if (tier) {
      query.tier = tier;
    }
    
    // Get random question using aggregation
    const questions = await Question.aggregate([
      { $match: query },
      { $sample: { size: 1 } },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      }
    ]);
    
    return questions.length > 0 ? questions[0] : null;
    
  } catch (error) {
    console.error('Error finding question by tier:', error);
    throw error;
  }
};

// Schedule daily questions for the next N days
const scheduleDailyQuestions = async (days = 7) => {
  try {
    const results = [];
    
    for (let i = 0; i < days; i++) {
      const targetDate = new Date();
      targetDate.setDate(targetDate.getDate() + i);
      targetDate.setHours(0, 0, 0, 0);
      
      // Check if question already exists for this date
      const existingQuestion = await DailyQuestion.findOne({ date: targetDate });
      
      if (existingQuestion) {
        results.push({
          date: targetDate,
          status: 'exists',
          question: existingQuestion
        });
        continue;
      }
      
      // Select and create question for this date
      const selectedQuestion = await selectDailyQuestion();
      
      if (selectedQuestion) {
        const dailyQuestion = new DailyQuestion({
          date: targetDate,
          question: selectedQuestion._id,
          category: selectedQuestion.category,
          tier: selectedQuestion.tier
        });
        
        await dailyQuestion.save();
        
        results.push({
          date: targetDate,
          status: 'created',
          question: dailyQuestion
        });
      } else {
        results.push({
          date: targetDate,
          status: 'failed',
          error: 'No suitable question found'
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('Error scheduling daily questions:', error);
    throw error;
  }
};

// Get daily question statistics
const getDailyQuestionStats = async (days = 30) => {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    startDate.setHours(0, 0, 0, 0);
    
    const stats = await DailyQuestion.aggregate([
      {
        $match: {
          date: { $gte: startDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: null,
          totalQuestions: { $sum: 1 },
          totalAttempts: { $sum: '$stats.totalAttempts' },
          totalCorrectAttempts: { $sum: '$stats.correctAttempts' },
          totalUniqueUsers: { $sum: '$stats.uniqueUsers' },
          averageSuccessRate: { $avg: { $divide: ['$stats.correctAttempts', '$stats.totalAttempts'] } },
          freeQuestions: {
            $sum: { $cond: [{ $eq: ['$tier', 'free'] }, 1, 0] }
          },
          premiumQuestions: {
            $sum: { $cond: [{ $eq: ['$tier', 'premium'] }, 1, 0] }
          }
        }
      }
    ]);
    
    return stats.length > 0 ? stats[0] : null;
    
  } catch (error) {
    console.error('Error getting daily question stats:', error);
    throw error;
  }
};

// Clean up old daily questions (keep last 90 days)
const cleanupOldDailyQuestions = async () => {
  try {
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    
    const result = await DailyQuestion.deleteMany({
      date: { $lt: ninetyDaysAgo }
    });
    
    console.log(`Cleaned up ${result.deletedCount} old daily questions`);
    return result;
    
  } catch (error) {
    console.error('Error cleaning up old daily questions:', error);
    throw error;
  }
};

module.exports = {
  updateDailyQuestion,
  selectDailyQuestion,
  scheduleDailyQuestions,
  getDailyQuestionStats,
  cleanupOldDailyQuestions
};
